@import 'tailwindcss';
@plugin 'tailwindcss-animate';
@custom-variant dark (&:is(.dark *));

@layer base {
  /* CSS Variables */
  /* https://tailwindcss.com/blog/tailwindcss-v4#css-theme-variables */
  :root {
    --color-orange: #ff5630;
    --color-jade: #00a76f;
    --color-midnight: #004b50;
    --color-platinum: #dfe3e8;
    --color-cultured: #f4f4f5cc;
    --color-cadet-grey: #919eab;
    --color-grey: #637381;
    --color-gunmetal: #1c252e;
  }

  /* Default theme */
  @theme inline {
    --color-primary: var(--color-gunmetal);
    --color-secondary: var(--color-grey);
    --color-text-primary: var(--color-gunmetal);
    --color-text-secondary: white;
    --color-error: var(--color-orange);
    --color-border: var(--color-cadet-grey);
    --color-platinum: var(--color-platinum);

    --animate-collapsible-down: collapsible-down 0.2s linear;
    --animate-collapsible-up: collapsible-up 0.2s linear;
  }

  /* Customized theme */
  html[data-theme='haha'] {
    --color-primary: var(--color-brown);
  }

  /* Animations */

  /* COLLAPSIBLE */
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
}

/**
  These colors will be automatically registered into <PERSON><PERSON>'s custom theme.
  See the config in src/config/theme.
  You can add more tokens and use it inside your Chakra components. Eg: <Text color={'awesome'}></Text>
 */

export const COLORS = {
  awesome: '#f72b50',
  rajah: '#ffa755',
  pastelGreen: '#68e365',
  hanBlue: '#496ecc',
  chargedBlue: '#1eb6e7',
  turquoise: '#40e5dc',
  mediumTurquoise: '#5bcfc5',
  eucalyptus: '#40d4a8',
  airBlue: '#709fba',
  hanPurple: '#461ee7',
  lavender: '#ba49ff',
  lenurple: '#b48dd3',
  darkOrchid: '#ac39d4',
  purplePizzaz: '#f04cf3',
  quickSilver: '#9fa4a6',
  chineseSilver: '#c8c8c8',
  aliceBlue: '#eefaf9',
  nyanza: '#e7fbe6',
} as const;

[{"id": "1", "header": "Cover page", "sectionType": "Cover page", "status": "In Process", "target": "Target", "limit": "Limit", "reviewer": "<PERSON>"}, {"id": "2", "header": "Table of contents", "sectionType": "Table of contents", "status": "Done", "target": "Target", "limit": "Limit", "reviewer": "<PERSON>"}, {"id": "3", "header": "Executive summary", "sectionType": "Narrative", "status": "Done", "target": "Target", "limit": "Limit", "reviewer": "<PERSON>"}, {"id": "4", "header": "Technical approach", "sectionType": "Narrative", "status": "Done", "target": "Target", "limit": "Limit", "reviewer": "<PERSON><PERSON>"}, {"id": "5", "header": "Design", "sectionType": "Narrative", "status": "In Process", "target": "Target", "limit": "Limit", "reviewer": "<PERSON><PERSON>"}, {"id": "6", "header": "Capabilities", "sectionType": "Narrative", "status": "In Process", "target": "Target", "limit": "Limit", "reviewer": "<PERSON><PERSON>"}, {"id": "7", "header": "Integration with existing systems", "sectionType": "Narrative", "status": "In Process", "target": "Target", "limit": "Limit", "reviewer": "<PERSON><PERSON>"}, {"id": "8", "header": "Innovation and Advantages", "sectionType": "Narrative", "status": "Done", "target": "Target", "limit": "Limit", "reviewer": "Assign reviewer"}, {"id": "9", "header": "Overview of EMR's Innovative Solutions", "sectionType": "Technical content", "status": "Done", "target": "Target", "limit": "Limit", "reviewer": "Assign reviewer"}, {"id": "10", "header": "Advanced Algorithms and Machine Learning", "sectionType": "Narrative", "status": "Done", "target": "Target", "limit": "Limit", "reviewer": "Assign reviewer"}]
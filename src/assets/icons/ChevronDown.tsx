import { HTMLAttributes } from 'react';

const ChevronDown = ({
  className,
}: {
  className?: HTMLAttributes<HTMLOrSVGElement>['className'];
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="m6 9 6 6 6-6"></path>
  </svg>
);

export default ChevronDown;

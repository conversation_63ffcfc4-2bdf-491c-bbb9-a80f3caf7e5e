@import 'tailwindcss';
@plugin 'tailwindcss-animate';
@custom-variant dark (&:is(.dark *));

@layer base {
  /* CSS Variables */
  /* https://tailwindcss.com/blog/tailwindcss-v4#css-theme-variables */
  :root {
    --color-orange: #ff5630;
    --color-jade: #00a76f;
    --color-midnight: #004b50;
    --color-platinum: #dfe3e8;
    --color-cultured: #f4f4f5cc;
    --color-cadet-grey: #919eab;
    --color-grey: #637381;
    --color-gunmetal: #1c252e;
  }

  /* Default theme */
  @theme inline {
    --color-primary: var(--color-gunmetal);
    --color-secondary: var(--color-grey);
    --color-text-primary: var(--color-gunmetal);
    --color-text-secondary: white;
    --color-error: var(--color-orange);
    --color-border: var(--color-cadet-grey);
    --color-platinum: var(--color-platinum);

    /* Chart colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --animate-collapsible-down: collapsible-down 0.2s linear;
    --animate-collapsible-up: collapsible-up 0.2s linear;
  }

  /* Customized theme */
  html[data-theme='haha'] {
    --color-primary: var(--color-brown);
  }

  /* Animations */

  /* COLLAPSIBLE */
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
}
